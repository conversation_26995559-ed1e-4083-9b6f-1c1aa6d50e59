# B2B Customer Portal E-commerce Architecture
## Technical Deep Dive: Multi-Tenant Customer Portal with Cart Management

---

## Executive Summary

This document provides a comprehensive technical architecture for the B2B customer portal, using e-commerce naming conventions (cart instead of quotation) while maintaining B2B functionality. The portal serves business customers like KAUST through a multi-tenant, multi-language, and highly customizable interface.

### Key Architectural Principles
- **Multi-Tenant Architecture**: Customer-specific branding and routing
- **E-commerce Conventions**: Cart-based workflow with B2B features
- **Role-Based Access**: Company users with different responsibilities
- **Multi-Language Support**: Arabic and English with RTL support
- **Theme Customization**: Customer-specific branding and colors
- **Multiple Cart Management**: Active cart switching and persistence

---

## Business Context & Requirements Analysis

### Customer Organization Structure
```mermaid
graph TB
    subgraph "KAUST Organization"
        A[Company Admin]
        B[Department Manager]
        C[Purchasing Representative]
        D[Finance Approver]
        E[End User/Requester]
    end
    
    subgraph "User Roles & Permissions"
        F[Create Users - Admin Only]
        G[Approve Purchases - Manager/Finance]
        H[Create Carts - All Users]
        I[Submit Orders - Rep/Manager]
        J[View Reports - Admin/Manager]
    end
    
    A --> F
    B --> G
    C --> H
    D --> G
    E --> H
    B --> I
    C --> I
    A --> J
    B --> J
```

### Multi-Cart Business Scenarios
1. **Department Separation**: Different carts for different departments
2. **Project-Based**: Separate carts for different projects
3. **Budget Cycles**: Monthly/quarterly budget-specific carts
4. **Approval Workflows**: Draft carts vs. submitted carts
5. **Negotiation Tracking**: Original cart vs. revised cart versions

---

## Frontend Architecture (Next.js)

### Multi-Tenant Routing Structure
```
customer-portal/
├── app/
│   ├── [customer-slug]/              # Dynamic customer routing
│   │   ├── layout.tsx               # Customer-specific layout
│   │   ├── page.tsx                 # Customer dashboard
│   │   ├── catalog/                 # Product catalog
│   │   │   ├── page.tsx            # Catalog listing
│   │   │   ├── [category]/         # Category pages
│   │   │   └── [product]/          # Product details
│   │   ├── cart/                    # Cart management
│   │   │   ├── page.tsx            # Active cart view
│   │   │   ├── list/               # All carts list
│   │   │   └── [cart-id]/          # Specific cart details
│   │   ├── orders/                  # Order management
│   │   │   ├── page.tsx            # Order history
│   │   │   └── [order-id]/         # Order details
│   │   ├── contracts/               # Contract management
│   │   │   ├── page.tsx            # Active contracts
│   │   │   └── [contract-id]/      # Contract details
│   │   ├── profile/                 # User & company profile
│   │   │   ├── page.tsx            # Profile overview
│   │   │   ├── company/            # Company settings
│   │   │   └── users/              # User management
│   │   └── analytics/               # Reports & analytics
│   ├── auth/                        # Authentication pages
│   │   ├── login/                  # Public login
│   │   ├── nafath/                 # Nafath integration
│   │   └── callback/               # Auth callbacks
│   └── api/                         # API routes
│       ├── auth/                   # Authentication APIs
│       ├── cart/                   # Cart management APIs
│       ├── catalog/                # Product APIs
│       └── customer/               # Customer APIs
├── components/
│   ├── layout/
│   │   ├── CustomerLayout.tsx      # Customer-specific layout
│   │   ├── Navigation.tsx          # Multi-language navigation
│   │   └── ThemeProvider.tsx       # Dynamic theming
│   ├── cart/
│   │   ├── CartManager.tsx         # Multiple cart management
│   │   ├── CartSwitcher.tsx        # Cart switching component
│   │   ├── CartItems.tsx           # Cart items display
│   │   └── CartNegotiation.tsx     # Negotiation interface
│   ├── catalog/
│   │   ├── ProductGrid.tsx         # Product listing
│   │   ├── ProductCard.tsx         # Product card component
│   │   ├── ProductFilter.tsx       # Advanced filtering
│   │   └── ProductSearch.tsx       # Search with autocomplete
│   ├── auth/
│   │   ├── PublicLogin.tsx         # Step 1: Public authentication
│   │   ├── NafathLogin.tsx         # Step 2: Nafath verification
│   │   └── UserContext.tsx         # User context provider
│   └── ui/
│       ├── ThemeCustomizer.tsx     # Customer theme components
│       ├── LanguageSwitcher.tsx    # Arabic/English switcher
│       └── RTLProvider.tsx         # RTL support
├── lib/
│   ├── auth/
│   │   ├── nafath.ts              # Nafath integration
│   │   ├── session.ts             # Session management
│   │   └── permissions.ts         # Role-based permissions
│   ├── cart/
│   │   ├── cartManager.ts         # Cart state management
│   │   ├── cartPersistence.ts     # Auto-save functionality
│   │   └── cartNegotiation.ts     # Negotiation logic
│   ├── theme/
│   │   ├── customerThemes.ts      # Customer-specific themes
│   │   ├── colorPalettes.ts       # Brand color management
│   │   └── rtlSupport.ts          # RTL layout support
│   └── i18n/
│       ├── config.ts              # Internationalization config
│       ├── ar.json                # Arabic translations
│       └── en.json                # English translations
├── stores/
│   ├── authStore.ts               # Authentication state
│   ├── cartStore.ts               # Cart management state
│   ├── customerStore.ts           # Customer context state
│   └── themeStore.ts              # Theme and language state
└── types/
    ├── auth.ts                    # Authentication types
    ├── cart.ts                    # Cart and order types
    ├── customer.ts                # Customer and user types
    └── theme.ts                   # Theme and branding types
```

### Multi-Language & RTL Support Architecture

#### Language Configuration
```typescript
// lib/i18n/config.ts
export const languages = {
  en: {
    code: 'en',
    name: 'English',
    direction: 'ltr',
    flag: '🇺🇸'
  },
  ar: {
    code: 'ar',
    name: 'العربية',
    direction: 'rtl',
    flag: '🇸🇦'
  }
}

export const defaultLanguage = 'en'
export const fallbackLanguage = 'en'
```

#### Theme Customization Architecture
```typescript
// types/theme.ts
export interface CustomerTheme {
  customerId: string
  primaryColor: string
  secondaryColor: string
  accentColor: string
  logoUrl: string
  faviconUrl: string
  fontFamily: string
  borderRadius: string
  customCSS?: string
  darkMode: {
    enabled: boolean
    primaryColor: string
    secondaryColor: string
    backgroundColor: string
  }
}

export interface ThemeConfig {
  light: CustomerTheme
  dark: CustomerTheme
  rtl: {
    enabled: boolean
    customizations: Record<string, string>
  }
}
```

---

## Authentication Architecture

### Two-Step Authentication Flow
```mermaid
sequenceDiagram
    participant User
    participant Portal
    participant PublicAuth
    participant Nafath
    participant Backend
    participant CustomerDB
    
    User->>Portal: Access /kaust portal
    Portal->>PublicAuth: Step 1: Public Login
    PublicAuth->>Portal: Basic session created
    
    Portal->>Nafath: Step 2: Nafath Verification
    Nafath->>Portal: National ID verified
    
    Portal->>Backend: Validate user & get B2B context
    Backend->>CustomerDB: Get customer permissions
    CustomerDB->>Backend: Return user roles & access
    Backend->>Portal: Full B2B session established
    
    Portal->>User: Access granted with customer context
```

### User Role Management
```typescript
// types/auth.ts
export interface UserRole {
  id: string
  name: string
  permissions: Permission[]
  level: 'admin' | 'manager' | 'user' | 'viewer'
}

export interface Permission {
  resource: 'cart' | 'order' | 'user' | 'contract' | 'analytics'
  actions: ('create' | 'read' | 'update' | 'delete' | 'approve')[]
  conditions?: {
    department?: string[]
    budgetLimit?: number
    approvalRequired?: boolean
  }
}

export interface B2BUser {
  id: string
  email: string
  nationalId: string
  nafathVerified: boolean
  customerId: string
  roles: UserRole[]
  department: string
  manager?: string
  preferences: {
    language: 'en' | 'ar'
    theme: 'light' | 'dark'
    notifications: NotificationSettings
  }
}
```

---

## Cart Management Architecture

### Multiple Cart System
```typescript
// types/cart.ts
export interface Cart {
  id: string
  name: string
  customerId: string
  userId: string
  status: CartStatus
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  expiresAt?: Date
  
  // Cart Content
  items: CartItem[]
  subtotal: number
  tax: number
  total: number
  currency: string
  
  // B2B Specific
  department: string
  project?: string
  budgetCode?: string
  approvalRequired: boolean
  contractId?: string
  
  // Negotiation
  negotiationThread: NegotiationMessage[]
  targetPrices: Record<string, number>
  vendorNotes?: string
  
  // Workflow
  submittedAt?: Date
  approvedAt?: Date
  rejectedAt?: Date
  convertedAt?: Date
}

export type CartStatus = 
  | 'draft'
  | 'submitted_for_review'
  | 'under_review'
  | 'revised'
  | 'approved'
  | 'rejected'
  | 'expired'
  | 'converted_to_order'

export interface CartItem {
  id: string
  productId: string
  variantId?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  targetPrice?: number
  specifications?: Record<string, any>
  notes?: string
  negotiationStatus?: 'pending' | 'accepted' | 'rejected'
}
```

### Cart State Management
```typescript
// stores/cartStore.ts
export interface CartStore {
  // State
  carts: Cart[]
  activeCartId: string | null
  loading: boolean
  error: string | null
  
  // Actions
  createCart: (name: string, options?: CartOptions) => Promise<Cart>
  switchCart: (cartId: string) => Promise<void>
  updateCart: (cartId: string, updates: Partial<Cart>) => Promise<void>
  deleteCart: (cartId: string) => Promise<void>
  
  // Cart Items
  addItem: (cartId: string, item: Omit<CartItem, 'id'>) => Promise<void>
  updateItem: (cartId: string, itemId: string, updates: Partial<CartItem>) => Promise<void>
  removeItem: (cartId: string, itemId: string) => Promise<void>
  bulkAddItems: (cartId: string, items: Omit<CartItem, 'id'>[]) => Promise<void>
  
  // Workflow
  submitCart: (cartId: string) => Promise<void>
  approveCart: (cartId: string) => Promise<void>
  rejectCart: (cartId: string, reason: string) => Promise<void>
  convertToOrder: (cartId: string) => Promise<string>
  
  // Negotiation
  addNegotiationMessage: (cartId: string, message: NegotiationMessage) => Promise<void>
  updateTargetPrice: (cartId: string, itemId: string, targetPrice: number) => Promise<void>
  
  // Persistence
  autoSave: (cartId: string) => Promise<void>
  loadCarts: () => Promise<void>
}
```

---

## Product Catalog Architecture

### Advanced Search & Filtering
```typescript
// types/catalog.ts
export interface ProductFilter {
  categories: string[]
  priceRange: {
    min: number
    max: number
    currency: string
  }
  availability: 'in_stock' | 'out_of_stock' | 'pre_order' | 'all'
  brands: string[]
  attributes: Record<string, string[]>
  contractOnly: boolean
  customerSpecific: boolean
}

export interface SearchQuery {
  query: string
  filters: ProductFilter
  sort: {
    field: 'name' | 'price' | 'popularity' | 'newest'
    direction: 'asc' | 'desc'
  }
  pagination: {
    page: number
    limit: number
  }
}

export interface Product {
  id: string
  sku: string
  name: string
  description: string
  category: ProductCategory
  brand: string
  images: ProductImage[]
  variants: ProductVariant[]
  attributes: ProductAttribute[]
  
  // B2B Pricing
  pricing: {
    basePrice: number
    customerPrice?: number
    contractPrice?: number
    currency: string
    priceBreaks: PriceBreak[]
  }
  
  // Availability
  availability: {
    status: 'in_stock' | 'out_of_stock' | 'pre_order'
    quantity: number
    leadTime: number
    location: string
  }
  
  // B2B Specific
  contractRequired: boolean
  minimumOrderQuantity: number
  maximumOrderQuantity?: number
  accessories: string[]
  relatedProducts: string[]
}
```
