# B2B E-Commerce Technical Solution Architecture
## Custom Next.js Frontend with Odoo Backend Integration

---

## Executive Summary

This document outlines a comprehensive technical solution for a B2B e-commerce platform using **Next.js** as the frontend customer portal and **Odoo 18** as the backend ERP system. The solution is designed for Zenith Arabia to serve business customers like KAUST through a secure, scalable, and feature-rich procurement portal.

### Key Architecture Principles
- **Separation of Concerns**: Frontend handles customer experience, backend manages business logic
- **API-First Design**: RESTful APIs for seamless integration
- **Security-First**: Multi-layer authentication with Nafath integration
- **Scalability**: Microservices-ready architecture
- **Maintainability**: Clear separation between customer portal and vendor management

---

## Business Requirements Summary

### Key Stakeholders
- **Zenith Arabia**: Vendor/Supplier managing catalogs, pricing, and fulfillment
- **KAUST**: Customer with multiple departments and purchasing workflows
- **Merchandiser**: Catalog and SKU management
- **Account Manager**: Contract and pricing management
- **KAUST Users**: Purchasing representatives and managers

### Core Features Required
1. **Multi-tenant B2B Portal** with customer-specific branding
2. **Frame Contract Management** with SLAs and compliance monitoring
3. **Customer-specific Pricing** with multiple price lists and dynamic discounts
4. **Quotation/Purchase Order Workflow** with negotiation capabilities (Cart = Quotation in B2B)
5. **Product Catalog Management** with variants and accessories
6. **2-Layer Secure Authentication** via Nafath + Odoo Portal integration
7. **Real-time Order Tracking** and history management

---

## System Architecture Overview

### High-Level Architecture Diagram

```mermaid
graph TB
    subgraph "Customer Layer"
        A[Next.js Customer Portal]
        B[Nafath Authentication]
        C[Customer Mobile App - Future]
    end

    subgraph "API Gateway Layer"
        D[API Gateway/Load Balancer]
        E[Authentication Middleware]
        F[Rate Limiting & Security]
    end

    subgraph "Backend Layer"
        G[Odoo 18 ERP System]
        H[Custom B2B Modules]
        I[Odoo Portal Framework]
    end

    subgraph "Data Layer"
        J[PostgreSQL Database]
        K[Redis Cache]
        L[File Storage]
    end

    subgraph "External Services"
        M[Nafath Identity Service]
        N[Email Service]
        O[SMS Gateway]
    end

    A --> D
    B --> M
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    H --> L
    E --> N
    E --> O
```

---

## Frontend Architecture (Next.js)

### Technology Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 18 with TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui components
- **State Management**: Zustand or Redux Toolkit
- **API Client**: Axios with interceptors
- **Authentication**: NextAuth.js with custom providers
- **Forms**: React Hook Form with Zod validation

### Frontend Structure
```
customer-portal/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected customer routes
│   ├── [customer]/        # Dynamic customer routes
│   │   ├── dashboard/     # Customer dashboard
│   │   ├── catalog/       # Product catalog
│   │   ├── quotations/    # Quotation management (Cart)
│   │   ├── orders/        # Order tracking
│   │   └── contracts/     # Contract viewing
│   ├── api/               # API routes for frontend
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── business/         # Business logic components
├── lib/                  # Utilities and configurations
│   ├── api/              # API client setup
│   ├── auth/             # Authentication logic
│   ├── utils/            # Helper functions
│   └── validations/      # Zod schemas
├── hooks/                # Custom React hooks
├── stores/               # State management
├── types/                # TypeScript definitions
└── public/               # Static assets
```

### Key Frontend Features

#### 1. Multi-Tenant Customer Portals
- **Dynamic Routing**: `/[customer-slug]` for branded experiences
- **Theme Configuration**: Customer-specific branding and colors
- **Content Management**: Customer-specific content and announcements

#### 2. Product Catalog & Search
- **Advanced Filtering**: Category, price range, availability
- **Search Functionality**: Full-text search with autocomplete
- **Product Variants**: Dynamic variant selection
- **Bulk Operations**: Add multiple products to cart

#### 3. Quotation Management (Cart System)
- **Multi-Cart Support**: Multiple active quotations
- **Draft Persistence**: Auto-save functionality
- **Quotation Workflow**: Submit, negotiate, approve, convert to order
- **Version History**: Track quotation changes and negotiations

#### 4. Customer Dashboard
- **Order History**: Past quotations and orders
- **Contract Management**: View active contracts and terms
- **Profile Management**: Company information and contacts
- **Analytics**: Spending reports and order statistics

---

## Backend Architecture (Odoo 18)

### Custom Module Structure
```
addons_b2b_ecommerce/
├── b2b_catalog/           # Product catalog management
├── b2b_contracts/         # Frame contract management
├── b2b_pricing/           # Customer-specific pricing
├── b2b_quotations/        # Quotation workflow
├── b2b_portal/            # Customer portal backend
├── b2b_auth/              # Authentication integration
└── b2b_api/               # REST API endpoints
```

### Core Backend Modules

#### 1. B2B Catalog Module (`b2b_catalog`)
**Models:**
- `b2b.product.template` - Extends product.template
- `b2b.product.category` - B2B specific categories
- `b2b.product.attribute` - Custom attributes

**Key Features:**
- Multi-SKU product management
- Dynamic attributes and variants
- Inventory integration
- Product visibility rules