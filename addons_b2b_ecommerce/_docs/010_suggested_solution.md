# B2B E-Commerce Technical Solution Architecture
## Complete Separation: Next.js Customer Portal + Custom Odoo Backend Module

---

## Executive Summary

This document outlines a comprehensive technical solution for a B2B e-commerce platform with **complete separation** between frontend and backend systems. The solution uses **Next.js** as a standalone customer portal and a **custom Odoo module** that replaces website_sale functionality, designed specifically for B2B procurement workflows.

### Key Architecture Principles
- **Complete Separation**: Next.js frontend is entirely independent from Odoo backend
- **API-First Design**: All communication through RESTful APIs
- **B2B-Focused**: Custom module designed specifically for B2B workflows, not extending e-commerce
- **Security-First**: Multi-layer authentication with Nafath integration
- **Scalability**: Independent scaling of frontend and backend systems
- **Maintainability**: Clear boundaries between customer portal and vendor management

---

## Business Requirements Summary

### Key Stakeholders
- **Zenith Arabia**: Vendor/Supplier managing catalogs, pricing, and fulfillment
- **KAUST**: Customer with multiple departments and purchasing workflows
- **Merchandiser**: Catalog and SKU management
- **Account Manager**: Contract and pricing management
- **KAUST Users**: Purchasing representatives and managers

### Core Features Required
1. **Multi-tenant B2B Portal** with customer-specific branding
2. **Frame Contract Management** with SLAs and compliance monitoring
3. **Customer-specific Pricing** with multiple price lists and dynamic discounts
4. **Quotation/Purchase Order Workflow** with negotiation capabilities (Cart = Quotation in B2B)
5. **Product Catalog Management** with variants and accessories
6. **2-Layer Secure Authentication** via Nafath + Odoo Portal integration
7. **Real-time Order Tracking** and history management

---

## System Architecture Overview

### High-Level Architecture Diagram

```mermaid
graph TB
    subgraph "Customer Layer"
        A[Next.js Customer Portal]
        B[Nafath Authentication]
        C[Customer Mobile App - Future]
    end

    subgraph "API Gateway Layer"
        D[API Gateway/Load Balancer]
        E[Authentication Middleware]
        F[Rate Limiting & Security]
    end

    subgraph "Backend Layer"
        G[Odoo 18 ERP System]
        H[Custom B2B Modules]
        I[Odoo Portal Framework]
    end

    subgraph "Data Layer"
        J[PostgreSQL Database]
        K[Redis Cache]
        L[File Storage]
    end

    subgraph "External Services"
        M[Nafath Identity Service]
        N[Email Service]
        O[SMS Gateway]
    end

    A --> D
    B --> M
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    H --> L
    E --> N
    E --> O
```

---

## Frontend Architecture (Next.js)

### Technology Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 18 with TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui components
- **State Management**: Zustand or Redux Toolkit
- **API Client**: Axios with interceptors
- **Authentication**: NextAuth.js with custom providers
- **Forms**: React Hook Form with Zod validation

### Frontend Structure
```
customer-portal/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Protected customer routes
│   ├── [customer]/        # Dynamic customer routes
│   │   ├── dashboard/     # Customer dashboard
│   │   ├── catalog/       # Product catalog
│   │   ├── quotations/    # Quotation management (Cart)
│   │   ├── orders/        # Order tracking
│   │   └── contracts/     # Contract viewing
│   ├── api/               # API routes for frontend
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── business/         # Business logic components
├── lib/                  # Utilities and configurations
│   ├── api/              # API client setup
│   ├── auth/             # Authentication logic
│   ├── utils/            # Helper functions
│   └── validations/      # Zod schemas
├── hooks/                # Custom React hooks
├── stores/               # State management
├── types/                # TypeScript definitions
└── public/               # Static assets
```

### Key Frontend Features

#### 1. Multi-Tenant Customer Portals
- **Dynamic Routing**: `/[customer-slug]` for branded experiences
- **Theme Configuration**: Customer-specific branding and colors
- **Content Management**: Customer-specific content and announcements

#### 2. Product Catalog & Search
- **Advanced Filtering**: Category, price range, availability
- **Search Functionality**: Full-text search with autocomplete
- **Product Variants**: Dynamic variant selection
- **Bulk Operations**: Add multiple products to cart

#### 3. Quotation Management (Cart System)
- **Multi-Cart Support**: Multiple active quotations
- **Draft Persistence**: Auto-save functionality
- **Quotation Workflow**: Submit, negotiate, approve, convert to order
- **Version History**: Track quotation changes and negotiations

#### 4. Customer Dashboard
- **Order History**: Past quotations and orders
- **Contract Management**: View active contracts and terms
- **Profile Management**: Company information and contacts
- **Analytics**: Spending reports and order statistics

---

## Backend Architecture (Odoo 18)

### Consolidated Custom Module: `b2b_portal_ecommerce`

Based on the study of existing Odoo modules (`purchase`, `sale_management`, `website_sale`), our solution will be implemented as a single comprehensive module that extends these core modules properly.

```
addons_b2b_ecommerce/
└── b2b_portal_ecommerce/           # Single comprehensive B2B module
    ├── __manifest__.py
    ├── models/
    │   ├── __init__.py
    │   ├── sale_order.py           # Extends sale.order for B2B quotations
    │   ├── purchase_order.py       # Extends purchase.order for vendor workflow
    │   ├── res_partner.py          # B2B customer extensions
    │   ├── res_users.py            # Portal user extensions
    │   ├── product_template.py     # B2B product extensions
    │   ├── product_pricelist.py    # Contract-based pricing
    │   ├── b2b_contract.py         # Frame contract management
    │   ├── b2b_quotation_state.py  # Quotation state machine
    │   └── website.py              # Website extensions
    ├── controllers/
    │   ├── __init__.py
    │   ├── main.py                 # Main B2B portal controller
    │   ├── auth.py                 # Nafath authentication
    │   ├── api.py                  # REST API endpoints
    │   ├── quotation.py            # Quotation management
    │   └── portal.py               # Portal extensions
    ├── security/
    │   ├── ir.model.access.csv
    │   └── security.xml
    ├── data/
    │   ├── quotation_states.xml
    │   └── email_templates.xml
    └── views/
        ├── sale_order_views.xml
        ├── portal_templates.xml
        └── b2b_contract_views.xml
```

### Core Model Extensions (Based on Odoo Study)

#### 1. Sale Order Extension (`models/sale_order.py`)
**Extends:** `sale.order` (following website_sale pattern)
**Purpose:** Transform sale orders into B2B quotations with multiple cart support

```python
class SaleOrder(models.Model):
    _inherit = 'sale.order'

    # B2B Quotation Fields
    quotation_type = fields.Selection([
        ('cart', 'Shopping Cart'),
        ('quotation', 'Submitted Quotation'),
        ('order', 'Confirmed Order')
    ], default='cart', string='Type')

    quotation_state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted for Review'),
        ('under_review', 'Under Review'),
        ('revised', 'Revised'),
        ('approved', 'Approved'),
        ('converted', 'Converted to Order'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired')
    ], default='draft', string='Quotation State')

    # Multiple Cart Support
    cart_name = fields.Char(string='Cart Name', default='Default Cart')
    is_active_cart = fields.Boolean(string='Active Cart', default=True)

    # B2B Specific
    b2b_customer_id = fields.Many2one('res.partner', string='B2B Customer')
    frame_contract_id = fields.Many2one('b2b.frame.contract', string='Frame Contract')
    quotation_validity_date = fields.Date(string='Quotation Valid Until')

    # Negotiation
    parent_quotation_id = fields.Many2one('sale.order', string='Parent Quotation')
    revision_number = fields.Integer(string='Revision Number', default=1)
    negotiation_notes = fields.Text(string='Negotiation Notes')
```

#### 2. Portal User Extension (`models/res_users.py`)
**Extends:** `res.users` (following portal pattern)
**Purpose:** Enhanced portal user with B2B context and multiple cart management

```python
class ResUsers(models.Model):
    _inherit = 'res.users'

    # B2B Portal Extensions
    b2b_customer_ids = fields.Many2many('res.partner', string='B2B Customers')
    primary_b2b_customer_id = fields.Many2one('res.partner', string='Primary B2B Customer')

    # Nafath Integration
    nafath_id = fields.Char(string='Nafath ID', index=True)
    national_id = fields.Char(string='National ID', index=True)
    nafath_verified = fields.Boolean(string='Nafath Verified', default=False)

    # Multiple Cart Management
    active_cart_ids = fields.One2many('sale.order', 'partner_id',
                                     domain=[('quotation_type', '=', 'cart'), ('state', '=', 'draft')])

    def get_or_create_cart(self, cart_name='Default Cart', b2b_customer_id=None):
        """Get existing cart or create new one for multiple cart support"""
        # Implementation for multiple cart management
        pass
```

#### 3. B2B Contract Management (`models/b2b_contract.py`)
**Purpose:** Frame contract management with SLA monitoring

```python
class B2BFrameContract(models.Model):
    _name = 'b2b.frame.contract'
    _description = 'B2B Frame Contract'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Contract Name', required=True)
    customer_id = fields.Many2one('res.partner', string='Customer', required=True)
    vendor_id = fields.Many2one('res.partner', string='Vendor', required=True)

    # Contract Details
    start_date = fields.Date(string='Start Date', required=True)
    end_date = fields.Date(string='End Date', required=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('terminated', 'Terminated')
    ], default='draft', tracking=True)

    # Pricing and Products
    pricelist_ids = fields.Many2many('product.pricelist', string='Contract Pricelists')
    product_category_ids = fields.Many2many('product.category', string='Allowed Categories')

    # SLA Terms
    delivery_sla_days = fields.Integer(string='Delivery SLA (Days)')
    response_sla_hours = fields.Integer(string='Response SLA (Hours)')

    # Performance Tracking
    quotation_ids = fields.One2many('sale.order', 'frame_contract_id', string='Quotations')
    performance_score = fields.Float(string='Performance Score', compute='_compute_performance')
```

### Multiple Cart Workflow Implementation

#### Cart Management Logic (Based on website_sale study)
```python
# In controllers/main.py - extending WebsiteSale pattern

class B2BPortalController(http.Controller):

    @http.route(['/b2b/cart/create'], type='json', auth='user', website=True)
    def create_new_cart(self, cart_name=None, **kwargs):
        """Create a new cart for the current user"""
        user = request.env.user
        b2b_customer = user.primary_b2b_customer_id

        # Deactivate current active cart
        current_cart = user.active_cart_ids.filtered('is_active_cart')
        if current_cart:
            current_cart.write({'is_active_cart': False})

        # Create new cart (following website_sale pattern)
        cart_values = {
            'partner_id': user.partner_id.id,
            'b2b_customer_id': b2b_customer.id,
            'quotation_type': 'cart',
            'cart_name': cart_name or f'Cart {len(user.active_cart_ids) + 1}',
            'is_active_cart': True,
            'state': 'draft'
        }

        new_cart = request.env['sale.order'].sudo().create(cart_values)
        request.session['sale_order_id'] = new_cart.id

        return {'cart_id': new_cart.id, 'cart_name': new_cart.cart_name}

    @http.route(['/b2b/cart/switch'], type='json', auth='user', website=True)
    def switch_cart(self, cart_id, **kwargs):
        """Switch to a different cart"""
        user = request.env.user
        target_cart = user.active_cart_ids.filtered(lambda c: c.id == cart_id)

        if target_cart:
            # Deactivate all carts
            user.active_cart_ids.write({'is_active_cart': False})
            # Activate target cart
            target_cart.write({'is_active_cart': True})
            request.session['sale_order_id'] = target_cart.id

            return {'success': True, 'cart_name': target_cart.cart_name}

        return {'success': False, 'error': 'Cart not found'}

    @http.route(['/b2b/quotation/submit'], type='json', auth='user', website=True)
    def submit_quotation(self, cart_id, **kwargs):
        """Submit cart as quotation for review"""
        cart = request.env['sale.order'].sudo().browse(cart_id)

        if cart.quotation_type == 'cart' and cart.state == 'draft':
            cart.write({
                'quotation_type': 'quotation',
                'quotation_state': 'submitted',
                'is_active_cart': False
            })

            # Create new default cart for continued shopping
            self.create_new_cart(cart_name='Default Cart')

            return {'success': True, 'quotation_id': cart.id}

        return {'success': False, 'error': 'Invalid cart state'}
```

### Quotation State Machine (Based on sale_management study)

#### Quotation States and Transitions
```python
# In models/b2b_quotation_state.py

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_submit_quotation(self):
        """Submit quotation for vendor review"""
        self.ensure_one()
        if self.quotation_state == 'draft':
            self.write({
                'quotation_state': 'submitted',
                'quotation_type': 'quotation'
            })
            self._send_quotation_notification()

    def action_approve_quotation(self):
        """Vendor approves quotation"""
        self.ensure_one()
        if self.quotation_state in ['submitted', 'under_review', 'revised']:
            self.write({'quotation_state': 'approved'})
            self._send_approval_notification()

    def action_revise_quotation(self, notes=None):
        """Create revised version of quotation"""
        self.ensure_one()
        if self.quotation_state in ['submitted', 'under_review']:
            # Create revision
            revision = self.copy({
                'parent_quotation_id': self.id,
                'revision_number': self.revision_number + 1,
                'quotation_state': 'revised',
                'negotiation_notes': notes
            })

            # Update original
            self.write({'quotation_state': 'revised'})

            return revision

    def action_convert_to_order(self):
        """Convert approved quotation to confirmed order"""
        self.ensure_one()
        if self.quotation_state == 'approved':
            self.write({
                'quotation_state': 'converted',
                'quotation_type': 'order'
            })
            # Follow standard sale order confirmation
            self.action_confirm()
```

---

## API Integration Layer

### Authentication Flow

```mermaid
sequenceDiagram
    participant Customer
    participant NextJS
    participant Nafath
    participant OdooAPI
    participant OdooPortal

    Customer->>NextJS: Login Request
    NextJS->>Nafath: Verify National ID
    Nafath->>NextJS: Identity Confirmed
    NextJS->>OdooAPI: Create/Update Portal User
    OdooAPI->>OdooPortal: Link to Customer Record
    OdooPortal->>NextJS: Return Access Token
    NextJS->>Customer: Authenticated Session
```

### API Endpoints Structure

#### Authentication Endpoints
```
POST /api/auth/nafath/verify
POST /api/auth/odoo/login
POST /api/auth/refresh
POST /api/auth/logout
```

#### Customer Management
```
GET /api/customers/profile
PUT /api/customers/profile
GET /api/customers/contracts
GET /api/customers/pricing
```

#### Product Catalog
```
GET /api/products
GET /api/products/{id}
GET /api/products/search
GET /api/categories
GET /api/products/{id}/variants
```

#### Quotation Management
```
GET /api/quotations
POST /api/quotations
GET /api/quotations/{id}
PUT /api/quotations/{id}
POST /api/quotations/{id}/submit
POST /api/quotations/{id}/approve
POST /api/quotations/{id}/convert-to-order
```

#### Order Management
```
GET /api/orders
GET /api/orders/{id}
GET /api/orders/{id}/status
GET /api/orders/{id}/documents
```

---

## Security Architecture

### Multi-Layer Authentication

#### Layer 1: Nafath Identity Verification
- **National ID Verification**: Saudi Arabia Nafath integration
- **Identity Assurance**: Government-backed identity verification
- **Session Management**: Secure session handling

#### Layer 2: Odoo Portal Authentication
- **Portal User Creation**: Automatic user provisioning
- **Role-Based Access**: Customer-specific permissions
- **API Token Management**: JWT-based authentication

### Security Measures
- **HTTPS Everywhere**: End-to-end encryption
- **API Rate Limiting**: Prevent abuse and DDoS
- **Input Validation**: Comprehensive data validation
- **CORS Configuration**: Secure cross-origin requests
- **Audit Logging**: Complete activity tracking

---

## Data Flow Architecture

### Customer Journey Data Flow

```mermaid
graph LR
    subgraph "Frontend (Next.js)"
        A[Customer Login]
        B[Browse Catalog]
        C[Create Quotation]
        D[Submit for Review]
    end

    subgraph "API Layer"
        E[Authentication API]
        F[Catalog API]
        G[Quotation API]
        H[Notification API]
    end

    subgraph "Backend (Odoo)"
        I[Portal User]
        J[Product Catalog]
        K[Quotation Workflow]
        L[Email System]
    end

    A --> E --> I
    B --> F --> J
    C --> G --> K
    D --> H --> L
```

### Quotation Workflow States

```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> SubmittedForReview : Customer submits
    SubmittedForReview --> UnderReview : Auto-transition
    UnderReview --> Revised : Vendor requests changes
    Revised --> UnderReview : Customer resubmits
    UnderReview --> Approved : Vendor accepts
    UnderReview --> Rejected : Vendor declines
    Approved --> ConvertedToOrder : Customer confirms
    Approved --> Expired : Validity expires
    Rejected --> [*]
    Expired --> [*]
    ConvertedToOrder --> [*]
```

---

## Deployment Architecture

### Infrastructure Components

#### Frontend Deployment
- **Platform**: Vercel or AWS Amplify
- **CDN**: CloudFront for global distribution
- **Environment**: Staging and Production
- **Monitoring**: Vercel Analytics or AWS CloudWatch

#### Backend Deployment
- **Platform**: Docker containers on AWS ECS or DigitalOcean
- **Database**: PostgreSQL with automated backups
- **Cache**: Redis for session and data caching
- **Load Balancer**: Nginx Proxy Manager
- **Monitoring**: Odoo built-in monitoring + external tools

### Security Infrastructure
- **Firewall**: AWS Security Groups or DigitalOcean Firewall
- **SSL/TLS**: Let's Encrypt certificates
- **VPN**: Secure admin access
- **Backup**: Automated daily backups with retention

---

## Integration Points

### Nafath Integration
- **Identity Verification**: Real-time ID verification
- **Session Management**: Secure session handling
- **Error Handling**: Graceful fallback mechanisms

### Odoo Portal Integration
- **User Provisioning**: Automatic customer account creation
- **Permission Management**: Role-based access control
- **Data Synchronization**: Real-time data sync

### Email & Notifications
- **Transactional Emails**: Order confirmations, status updates
- **SMS Notifications**: Critical updates via SMS gateway
- **In-App Notifications**: Real-time portal notifications

---

## Suggested Backend API Controllers (Endpoint Descriptions)

### Consolidated API Structure (`controllers/api.py`)

Based on the study of Odoo's controller patterns, our API will extend the existing portal and website_sale controllers while adding B2B-specific functionality.

### 1. Authentication & Session Controller (`/api/v1/auth/`)

#### Nafath Authentication (extends portal authentication)
- `POST /nafath/initiate` - Start Nafath authentication flow with state management
- `POST /nafath/callback` - Handle Nafath authentication callback and create portal session
- `POST /nafath/verify` - Verify Nafath token and link to Odoo portal user

#### B2B Portal Session Management
- `POST /portal/validate` - Validate portal user and return B2B customer context
- `POST /portal/switch-customer` - Switch active B2B customer context
- `GET /portal/profile` - Get complete user profile with B2B permissions
- `POST /session/refresh` - Refresh session tokens with updated context

### 2. Multiple Cart Management Controller (`/api/v1/carts/`)

#### Cart Operations (extends website_sale cart functionality)
- `GET /` - List all user carts (draft quotations)
- `POST /create` - Create new named cart for multiple cart support
- `POST /switch/{cart_id}` - Switch to different active cart
- `GET /{cart_id}` - Get specific cart details and line items
- `DELETE /{cart_id}` - Delete empty cart

#### Cart Line Management (follows website_sale pattern)
- `POST /{cart_id}/lines` - Add product to specific cart
- `PUT /{cart_id}/lines/{line_id}` - Update cart line quantity/details
- `DELETE /{cart_id}/lines/{line_id}` - Remove line from cart
- `POST /{cart_id}/bulk-add` - Add multiple products to cart

### 3. Quotation Workflow Controller (`/api/v1/quotations/`)

#### Quotation Lifecycle (extends sale.order workflow)
- `GET /` - List quotations with state filtering (submitted, approved, etc.)
- `POST /submit/{cart_id}` - Submit cart as quotation for vendor review
- `GET /{quotation_id}` - Get detailed quotation with negotiation history
- `POST /{quotation_id}/approve` - Approve quotation (vendor action)
- `POST /{quotation_id}/revise` - Create revised quotation version
- `POST /{quotation_id}/convert` - Convert approved quotation to confirmed order

#### Negotiation Management
- `GET /{quotation_id}/revisions` - Get quotation revision history
- `POST /{quotation_id}/negotiate` - Add negotiation notes and request changes
- `GET /{quotation_id}/pdf` - Generate quotation PDF document
- `POST /{quotation_id}/duplicate` - Create copy for new quotation

### 4. Product Catalog Controller (`/api/v1/products/`)

#### B2B Catalog Access (extends website_sale product access)
- `GET /` - Get paginated product list with B2B customer pricing
- `GET /{id}` - Get detailed product with customer-specific pricing
- `GET /search` - Advanced search with B2B filters (contract products, etc.)
- `GET /categories` - Get category hierarchy with B2B access rules

#### B2B Pricing & Availability
- `GET /{id}/pricing` - Get contract-based pricing tiers and discounts
- `GET /{id}/availability` - Get real-time stock with B2B lead times
- `GET /{id}/contracts` - Get contracts that include this product
- `POST /bulk-pricing` - Get pricing for multiple products

### 5. Contract & Customer Controller (`/api/v1/customers/`)

#### Customer Profile (extends portal functionality)
- `GET /profile` - Get B2B customer company profile
- `PUT /profile` - Update customer profile information
- `GET /users` - List customer portal users and roles
- `GET /permissions` - Get current user's B2B permissions

#### Contract Management
- `GET /contracts` - List active frame contracts
- `GET /contracts/{id}` - Get detailed contract terms and SLA status
- `GET /contracts/{id}/products` - Get products available under contract
- `GET /contracts/{id}/performance` - Get contract performance metrics

### 6. Order Tracking Controller (`/api/v1/orders/`)

#### Order Management (extends sale portal functionality)
- `GET /` - List confirmed orders with advanced filtering
- `GET /{id}` - Get detailed order with tracking information
- `GET /{id}/status` - Get real-time order status updates
- `GET /{id}/documents` - Get order documents (invoices, delivery notes)

#### Reorder Functionality
- `POST /{id}/reorder` - Create new cart from existing order
- `POST /{id}/reorder-lines` - Add specific order lines to current cart
- `GET /{id}/reorder-availability` - Check product availability for reorder

### 7. Analytics & Reporting Controller (`/api/v1/analytics/`)

#### Dashboard Data
- `GET /dashboard` - Get customer dashboard KPIs and metrics
- `GET /spending` - Get spending analysis by period/category
- `GET /quotation-stats` - Get quotation conversion rates and trends
- `GET /contract-compliance` - Get SLA compliance and performance data

#### Business Intelligence
- `GET /reports/spending` - Generate detailed spending reports
- `GET /reports/orders` - Generate order performance analytics
- `GET /reports/products` - Generate product usage and preference analysis

### API Implementation Pattern (Based on Odoo Study)

```python
# controllers/api.py - Following Odoo controller patterns

from odoo import http, fields, _
from odoo.http import request
from odoo.addons.website_sale.controllers.main import WebsiteSale
from odoo.addons.portal.controllers.portal import CustomerPortal

class B2BPortalAPI(CustomerPortal):
    """Extends portal functionality for B2B operations"""

    @http.route(['/api/v1/carts/create'], type='json', auth='user', website=True)
    def create_cart(self, cart_name=None, **kwargs):
        """Create new cart following website_sale pattern"""
        user = request.env.user
        if not user.primary_b2b_customer_id:
            return {'error': 'No B2B customer access'}

        # Deactivate current cart
        current_carts = request.env['sale.order'].search([
            ('partner_id', '=', user.partner_id.id),
            ('quotation_type', '=', 'cart'),
            ('is_active_cart', '=', True),
            ('state', '=', 'draft')
        ])
        current_carts.write({'is_active_cart': False})

        # Create new cart (following website.sale_get_order pattern)
        cart_values = request.website._prepare_sale_order_values(user.partner_id)
        cart_values.update({
            'quotation_type': 'cart',
            'cart_name': cart_name or f'Cart {len(current_carts) + 1}',
            'is_active_cart': True,
            'b2b_customer_id': user.primary_b2b_customer_id.id
        })

        new_cart = request.env['sale.order'].sudo().create(cart_values)
        request.session['sale_order_id'] = new_cart.id

        return {
            'success': True,
            'cart_id': new_cart.id,
            'cart_name': new_cart.cart_name
        }

    @http.route(['/api/v1/quotations/submit/<int:cart_id>'], type='json', auth='user')
    def submit_quotation(self, cart_id, **kwargs):
        """Submit cart as quotation following sale.order state pattern"""
        cart = request.env['sale.order'].browse(cart_id)

        # Verify access and state
        if cart.partner_id != request.env.user.partner_id:
            return {'error': 'Access denied'}

        if cart.quotation_type != 'cart' or cart.state != 'draft':
            return {'error': 'Invalid cart state'}

        # Submit quotation (following sale order workflow)
        cart.write({
            'quotation_type': 'quotation',
            'quotation_state': 'submitted',
            'is_active_cart': False,
            'date_order': fields.Datetime.now()
        })

        # Send notification (following sale order message pattern)
        cart.message_post(
            body=_('Quotation submitted for review by %s') % request.env.user.name,
            message_type='notification'
        )

        # Create new default cart for continued shopping
        self.create_cart(cart_name='Default Cart')

        return {
            'success': True,
            'quotation_id': cart.id,
            'quotation_name': cart.name
        }

class B2BWebsiteSale(WebsiteSale):
    """Extends website_sale for B2B functionality"""

    @http.route(['/api/v1/products/b2b-pricing/<int:product_id>'], type='json', auth='user')
    def get_b2b_pricing(self, product_id, **kwargs):
        """Get B2B customer-specific pricing"""
        user = request.env.user
        product = request.env['product.product'].browse(product_id)

        if not user.primary_b2b_customer_id:
            return {'error': 'No B2B customer access'}

        # Get contract-based pricing
        contracts = user.primary_b2b_customer_id.frame_contract_ids.filtered(
            lambda c: c.state == 'active' and
            product.categ_id in c.product_category_ids
        )

        pricing_tiers = []
        for contract in contracts:
            for pricelist in contract.pricelist_ids:
                price = pricelist._get_product_price(product, 1.0, user.partner_id)
                pricing_tiers.append({
                    'contract_name': contract.name,
                    'pricelist_name': pricelist.name,
                    'price': price,
                    'currency': pricelist.currency_id.name
                })

        return {
            'product_id': product_id,
            'pricing_tiers': pricing_tiers,
            'base_price': product.list_price
        }
```

---

## Performance Optimization

### Frontend Optimization
- **Code Splitting**: Route-based code splitting
- **Image Optimization**: Next.js Image component
- **Caching**: Browser and CDN caching strategies
- **Bundle Analysis**: Regular bundle size monitoring

### Backend Optimization
- **Database Indexing**: Optimized database queries
- **Caching Strategy**: Redis for frequently accessed data
- **API Optimization**: Efficient API response design
- **Background Jobs**: Async processing for heavy operations

---

## Development Workflow

### Frontend Development
1. **Local Development**: Next.js dev server with hot reload
2. **API Mocking**: Mock API responses for development
3. **Testing**: Jest + React Testing Library
4. **Deployment**: Automatic deployment via Git integration

### Backend Development
1. **Local Development**: Odoo development environment
2. **Module Development**: Custom module development workflow
3. **Testing**: Odoo test framework
4. **Deployment**: Docker-based deployment pipeline

---

## Monitoring & Analytics

### Application Monitoring
- **Frontend**: Vercel Analytics, Sentry for error tracking
- **Backend**: Odoo logs, custom monitoring dashboards
- **API**: Request/response monitoring, performance metrics
- **Database**: Query performance monitoring

### Business Analytics
- **Customer Behavior**: Portal usage analytics
- **Sales Metrics**: Quotation conversion rates
- **Performance KPIs**: Order processing times
- **Contract Compliance**: SLA monitoring

---

## Future Enhancements

### Phase 2 Features
- **Mobile Application**: React Native customer app
- **Advanced Analytics**: Business intelligence dashboard
- **AI Integration**: Smart product recommendations
- **Multi-Language**: Internationalization support

### Scalability Considerations
- **Microservices**: Break down into smaller services
- **Event-Driven Architecture**: Implement event sourcing
- **Multi-Region**: Global deployment strategy
- **API Gateway**: Advanced API management

---

## Conclusion

This technical solution provides a robust, scalable, and secure foundation for a B2B e-commerce platform based on a thorough study of Odoo's core modules (`purchase`, `sale_management`, `website_sale`). The solution properly extends existing Odoo functionality while adding B2B-specific features.

### Key Technical Decisions Based on Odoo Study:

#### 1. **Consolidated Module Approach**
- **Single Module**: `b2b_portal_ecommerce` consolidates all B2B functionality
- **Proper Inheritance**: Extends `sale.order`, `res.users`, `website_sale` following Odoo patterns
- **Maintainability**: Easier to maintain and deploy as a single cohesive module

#### 2. **Multiple Cart Implementation**
- **Based on website_sale**: Extends existing cart functionality with multiple cart support
- **Session Management**: Follows Odoo's session pattern with `sale_order_id` tracking
- **State Management**: Uses `quotation_type` field to distinguish carts from quotations

#### 3. **Quotation Workflow**
- **Sale Order Extension**: Quotations are sale orders with additional states and workflow
- **State Machine**: Implements proper state transitions following Odoo's workflow patterns
- **Portal Integration**: Extends portal functionality for customer access

#### 4. **Portal User Enhancement**
- **B2B Context**: Adds B2B customer relationships to portal users
- **Nafath Integration**: Implements 2-layer authentication with government ID verification
- **Permission System**: Role-based access control for B2B operations

### Workflow Summary:

1. **Portal User Login** → Nafath verification + Odoo portal authentication
2. **Browse Catalog** → B2B customer-specific products with contract pricing
3. **Multiple Carts** → Create/switch between multiple named carts
4. **Add to Cart** → Products added to active cart (draft sale.order)
5. **Submit Quotation** → Cart converted to quotation state for vendor review
6. **Negotiation** → Revision system with approval workflow
7. **Order Conversion** → Approved quotations become confirmed sale orders

### Key Benefits:

1. **Odoo-Native**: Leverages existing Odoo functionality and patterns
2. **Scalable**: Built on proven Odoo architecture with proper inheritance
3. **Maintainable**: Single module with clear separation of concerns
4. **B2B-Focused**: Multiple carts, quotation workflow, contract-based pricing
5. **Portal-Ready**: Extends existing portal functionality for B2B customers
6. **API-First**: RESTful APIs for Next.js frontend integration
7. **Secure**: 2-layer authentication with Nafath + Odoo portal security

### Implementation Advantages:

- **Faster Development**: Builds on existing Odoo functionality
- **Lower Risk**: Uses proven Odoo patterns and workflows
- **Better Integration**: Native integration with Odoo's ERP features
- **Easier Maintenance**: Single module with standard Odoo structure
- **Future-Proof**: Can leverage future Odoo enhancements and updates

This architecture ensures that the B2B e-commerce platform aligns with both Odoo best practices and the specific requirements for Zenith Arabia's customers like KAUST, providing a solid foundation for B2B procurement processes.